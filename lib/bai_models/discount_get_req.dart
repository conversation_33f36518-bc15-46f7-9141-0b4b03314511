// To parse this JSON data, do
//
//     final discountGetReq = discountGetReqFromJson(jsonString);

import 'dart:convert';

DiscountGetReq discountGetReqFromJson(String str) =>
    DiscountGetReq.fromJson(json.decode(str));

String discountGetReqToJson(DiscountGetReq data) => json.encode(data.toJson());

class DiscountGetReq {
  int? ordrGrpId;
  int? sellerId;
  int categoryId;
  int? offerId;

  DiscountGetReq({
    this.ordrGrpId,
    this.sellerId,
    required this.categoryId,
    this.offerId,
  });

  factory DiscountGetReq.fromJson(Map<String, dynamic> json) => DiscountGetReq(
        ordrGrpId: json["ordrGrpId"],
        sellerId: json["sellerId"],
        categoryId: json["categoryId"],
        offerId: json["offerId"],
      );

  Map<String, dynamic> toJson() => {
        "ordrGrpId": ordrGrpId,
        "sellerId": sellerId,
        "categoryId": categoryId,
        "offerId": offerId,
      };
}

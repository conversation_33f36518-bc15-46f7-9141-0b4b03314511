import 'package:connectone/bai_blocs/calculator/cubit/calculator_cubit.dart';
import 'package:connectone/bai_blocs/offers/cubit/offers_cubit.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_screens/buyer_offers_page.dart';
import 'package:connectone/core/bai_widgets/discount_transportation_dialog.dart';
import 'package:connectone/bai_models/summary_res.dart';
import 'package:connectone/bai_models/view_offer_req.dart';
import 'package:connectone/bai_screens/single_mr_screen.dart';
// import 'package:connectone/bai_screens/quotes_dialog.dart';
import 'package:connectone/core/bai_widgets/buyer_quotes_dialog.dart';
import 'package:connectone/core/bai_widgets/roundoff_discount_dialog.dart';
import 'package:connectone/core/bai_widgets/seller_quotes_dialog.dart';
import 'package:connectone/core/push_notifications/notification_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class SummaryTable extends StatefulWidget {
  final List<SummaryResponse> res;
  final int index;
  final Function? prchOrdrOffrIds;
  final bool? fromAssign;
  final Content content;
  final String statusCd;
  final DateTime date;
  final String? categoryId;
  final String? splitName;
  final bool steelMr;

  const SummaryTable({
    Key? key,
    required this.res,
    required this.index,
    this.prchOrdrOffrIds,
    this.fromAssign = false,
    required this.content,
    required this.statusCd,
    required this.date,
    this.categoryId,
    this.splitName,
    required this.steelMr,
  }) : super(key: key);

  @override
  State<SummaryTable> createState() => _SummaryTableState();
}

class _SummaryTableState extends State<SummaryTable> {
  var isLowest = true;

  @override
  Widget build(BuildContext context) {
    // var mrStatus = widget.content.statusCd ?? "";
    // var enableSubmit = ["QUOT", "NEGO", "VASS"].contains(mrStatus);
    var showOrangeBar = widget.res[widget.index].isOfferApplied ?? false;
    return BlocProvider(
      create: (context) => CalculatorCubit(),
      child: InteractiveViewer(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BuyerNameBox(
                name: widget.res[widget.index].summary?.buyerName ?? ''),
            SiteNameBox(
              name:
                  "${widget.res[widget.index].summary?.siteName}, ${widget.res[widget.index].summary?.siteAddress?.getFormattedAddress()}",
            ),
            SplitNameBox(
                name: widget.res[widget.index].summary?.categoryName ?? '',
                splitName: widget.splitName),
            SiteAccess(
                name: widget.res[widget.index].summary?.siteAccess ?? ''),
            MrAndDateBox(
              mrnum: widget.res[widget.index].summary?.cappCategoryId != null
                  ? "${widget.res[widget.index].summary?.mrNo} - ${widget.res[widget.index].summary?.cappCategoryId}"
                  : "${widget.res[widget.index].summary?.mrNo}",
              date: widget.res[widget.index].summary?.mrDate?.toDeliveryOn() ??
                  '',
            ),
            VendorNameBox(
              vendorname: isBuyer()
                  ? widget.res[widget.index].summary?.vendorName ?? 'N/A'
                  : widget.res[widget.index].summary?.buyerName ?? 'N/A',
              vendorPhone: int.tryParse(
                isBuyer()
                    ? widget.res[widget.index].summary?.vendorPhone ?? ""
                    : widget.res[widget.index].summary?.buyerPhone ?? "",
              ),
            ),
            QuotationNoAndDateBox(
              quotation: widget.res[widget.index].vendorGroupedProducts?[0]
                      .matchedProducts?[0].details?[0].prchOrdrOffrId
                      ?.toString() ??
                  '',
              quotationDate:
                  widget.res[widget.index].summary!.quoteDate?.toDeliveryOn() ??
                      'N/A',
            ),
            SummaryQuote(
              isLowest: isLowest,
              show: showOrangeBar,
            ),
            // Product table that needs horizontal scrolling
            ProductTable(
              category: widget.res[widget.index].summary?.categoryName ?? '',
              matchedProductslist: widget.res[widget.index]
                      .vendorGroupedProducts?[0].matchedProducts ??
                  [],
              subtotal: '${widget.res[widget.index].summary?.subtotal ?? ''}',
              total: '${widget.res[widget.index].summary?.total ?? ''}',
              transportation:
                  '${widget.res[widget.index].summary?.transportationCharge ?? ''}',
              gst: '${widget.res[widget.index].summary?.gst ?? ''}',
              discount: '${widget.res[widget.index].summary?.discount ?? ''}',
              buyer: widget.res[widget.index].summary?.vendorName ?? '',
              isLowest: (val) {
                setState(() {
                  isLowest = val;
                });
              },
              statusCd: widget.statusCd,
              prchOrdrOffrIds: (val) {},
              fromAssign: widget.fromAssign,
              content: widget.content,
              date: widget.date,
              categoryId: widget.categoryId,
              steelMr: widget.steelMr,
            ),
          ],
        ),
      ),
    );
  }
}
///////////

class ProductTable extends StatefulWidget {
  final List<MatchedProduct>? matchedProductslist;
  final String subtotal;
  final String discount;
  final String transportation;
  final String gst;
  final String total;
  final String category;
  final String buyer;
  final Function isLowest;
  final Function prchOrdrOffrIds;
  final bool? fromAssign;
  final Content content;
  final String statusCd;
  final DateTime date;
  final String? categoryId;
  final bool steelMr;

  const ProductTable({
    Key? key,
    required this.subtotal,
    required this.discount,
    required this.transportation,
    required this.gst,
    required this.total,
    this.matchedProductslist,
    required this.category,
    required this.buyer,
    required this.isLowest,
    required this.prchOrdrOffrIds,
    this.fromAssign,
    required this.content,
    required this.statusCd,
    required this.date,
    required this.categoryId,
    required this.steelMr,
  }) : super(key: key);

  @override
  State<ProductTable> createState() => _ProductTableState();
}

class _ProductTableState extends State<ProductTable> {
  // Variable to hold the current total price
  double currentSubTotal = 0.0;
  double currentTotal = 0.0;
  double gstTotal = 0.0;
  double totalWithoutGst = 0.0;

  List<Detail?> selectedDetails = [];

  @override
  void initState() {
    super.initState();

    // Initialize the current total price
    currentTotal = 0.0; // Initialize currentTotal to 0
    gstTotal = 0.0; // Initialize gstTotal to 0

    totalWithoutGst = calculateTotalWithoutGst(
      total: currentTotal,
      discount: totalDiscount(),
      transportation: double.parse(widget.transportation),
    );
    currentSubTotal = calculateTotalPrice(
      total: currentTotal,
      discount: totalDiscount(),
      transportation: double.parse(widget.transportation),
      gst: gstTotal,
    );
  }

  double totalDiscount() {
    var discount = double.parse(widget.discount);
    return discount;
  }

  // Function to calculate total price with GST
  double calculateTotalPrice({
    required double total, // This is subtotal before GST
    required double discount,
    required double transportation,
    required double gst,
  }) {
    return (total + transportation + gst - discount);
  }

  // Function to calculate total price without GST
  double calculateTotalWithoutGst({
    required double total, // This is subtotal before GST
    required double discount,
    required double transportation,
  }) {
    return (total + transportation - discount);
  }

  bool isLowest = true;

  void _checkIfLowest() {
    num sum = 0;

    for (var matchedProduct in widget.matchedProductslist ?? []) {
      if (matchedProduct.details?[0].subProducts?.isNotEmpty ?? false) {
        sum += matchedProduct.details?[0].subProducts?.fold(
                0.0,
                (total, subProduct) =>
                    total + (subProduct.subProductTotalPrice ?? 0.0)) ??
            0.0;
      } else {
        sum += matchedProduct.details?[0].offerPrice ?? 0;
      }
    }

    setState(() {
      isLowest = (currentTotal == sum);
    });

    widget.isLowest(isLowest);
  }

  final GlobalKey<_ProductItemState> _productItemKey =
      GlobalKey<_ProductItemState>();

  void resetProductItem() {
    _productItemKey.currentState?.reset();
  }

  List<int> offerIdList = [];

  // num subtotal1 = 0;
  // num transportationCharge = 0;
  // num discount = 0;
  // num subtotal2 = 0;
  // num gst = 0;
  // num totalAmount = 0;

  // "subTotalWithoutGst": 6350.0,
  // "discount": 0.0,
  // "totalAfterDiscount": 6350.0,
  // "gst": 0.0,
  // "transportation": 0.0,
  // "loading": 0.0,
  // "gstOnTransportation": 0.0,
  // "total": 6350.0

  num subTotalWithoutGst = 0;
  num discount = 0;
  num totalAfterDiscount = 0;
  num gst = 0;
  num transportation = 0;
  num loading = 0;
  num gstOnTransportation = 0;
  num roundoffDiscount = 0;
  num total = 0;

  @override
  Widget build(BuildContext context) {
    TextStyle style1 = const TextStyle(
      overflow: TextOverflow.ellipsis,
      color: Colors.white,
      fontWeight: FontWeight.bold,
      fontSize: 12,
    );
    TextStyle style2 = const TextStyle(
      overflow: TextOverflow.ellipsis,
      color: Colors.black,
      fontWeight: FontWeight.bold,
      fontSize: 12,
    );

    return SizedBox(
      width: MediaQuery.of(context).size.width - 38,
      child: Column(
        children: [
          //heading
          SizedBox(
            height: 40,
            child: Row(
              children: [
                Expanded(
                    flex: 10,
                    child: Container(
                      height: 40,
                      decoration:
                          BoxDecoration(border: Border.all(width: 0.75)),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Text(
                          'Category',
                          style: style2,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    )),
                Expanded(
                    flex: 8,
                    child: Container(
                      height: 40,
                      decoration:
                          BoxDecoration(border: Border.all(width: 0.75)),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Text(
                          widget.category,
                          style: style2,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ))
              ],
            ),
          ),
          Container(
            decoration: BoxDecoration(
                border: Border.all(width: 0.75),
                color: AppColors.primaryColorOld),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    decoration: const BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: Colors.white,
                          width: 0.75,
                        ),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        children: [
                          Text(
                            '#',
                            style: style1,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: Container(
                    decoration: const BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: Colors.white,
                          width: 0.75,
                        ),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        children: [
                          Text(
                            'Qty',
                            style: style1,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: Container(
                    decoration: const BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: Colors.white,
                          width: 0.75,
                        ),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        children: [
                          Text(
                            'Unit',
                            style: style1,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: Container(
                    decoration: const BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: Colors.white,
                          width: 0.75,
                        ),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        children: [
                          Text(
                            'Price',
                            style: style1,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: Container(
                    decoration: const BoxDecoration(),
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        children: [
                          Text(
                            'Amount',
                            style: style1,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Product Row with Callback
          ProductItem(
            key: _productItemKey,
            fromAssign: widget.fromAssign,
            match: widget.matchedProductslist,
            tnChrg: num.tryParse(widget.transportation) ?? 0,
            dcnt: num.tryParse(widget.discount) ?? 0,
            onSumChange: (newOfferPriceSum, newGstSum) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                setState(() {
                  currentTotal = newOfferPriceSum;
                  gstTotal = newGstSum;
                  totalWithoutGst = calculateTotalWithoutGst(
                    total: currentTotal,
                    discount: totalDiscount(),
                    transportation: double.parse(widget.transportation),
                  );
                  currentSubTotal = calculateTotalPrice(
                    total: currentTotal,
                    discount: totalDiscount(),
                    transportation: double.parse(widget.transportation),
                    gst: gstTotal,
                  );
                });
                _checkIfLowest();
              });
            },
            statusCd: widget.statusCd,
            onDetailChange: (selectedDetailsInfo) {
              var ids = [];
              WidgetsBinding.instance.addPostFrameCallback((_) {
                for (var element in selectedDetailsInfo) {
                  ids.add(element?.prchOrdrOffrId ?? 0);
                }
                setState(() {
                  selectedDetails = selectedDetailsInfo;
                  offerIdList = List<int>.from(ids);
                });
              });
            },
            prchOrdrOffrId: (val) {
              widget.prchOrdrOffrIds(val);
            },
          ),

          // Subtotal, Discount, Transportation, GST, Total
          BlocConsumer<CalculatorCubit, CalculatorState>(
            listener: (context, state) {
              if (state is CalculationDone) {
                var data = state.res.data;
                setState(() {
                  subTotalWithoutGst =
                      data?.subtotalWithoutGst?.toDouble() ?? 0;
                  discount = data?.discount?.toDouble() ?? 0;
                  totalAfterDiscount =
                      data?.totalAfterDiscount?.toDouble() ?? 0;
                  gst = data?.totalGst?.toDouble() ?? 0;
                  transportation = data?.transportation?.toDouble() ?? 0;
                  loading = data?.loading?.toDouble() ?? 0;
                  gstOnTransportation =
                      data?.gstTransportationAndLoading?.toDouble() ?? 0;
                  roundoffDiscount = data?.roundoffDiscount?.toDouble() ?? 0;
                  total = data?.total?.toDouble() ?? 0;
                });
              }
            },
            builder: (context, state) {
              return BlocBuilder<CalculatorCubit, CalculatorState>(
                builder: (context, state) {
                  return Container(
                    decoration: const BoxDecoration(),
                    child: Column(
                      children: [
                        buildRow('Sub Total Without GST',
                            subTotalWithoutGst.toStringAsFixed(2), style2),
                        GestureDetector(
                          onTap: () {
                            // alert(getVendorId());
                            if (isBuyer()) {
                              return;
                            }
                            showDialog(
                                context: context,
                                builder: (context) {
                                  return DiscountTransportationDialog(
                                    orderGroupId:
                                        widget.content.orderGroupId?.toInt() ??
                                            0,
                                    sellerId: getVendorId(),
                                    categoryId: widget.content.cappCategoriesId
                                            ?.toInt() ??
                                        0,
                                    offerIds: offerIdList,
                                  );
                                }).then((value) {
                              if (value == true) {
                                var req = ViewOfferReq(
                                  variant1OptionGroupName: [],
                                  variant1OptionNames: [],
                                  variant2OptionGroupName: [],
                                  variant2OptionNames: [],
                                  variant3OptionGroupName: [],
                                  variant3OptionNames: [],
                                );
                                context.read<OffersCubit>().loadSellerOffers(
                                      widget.content.prchOrdrId.toString(),
                                      req,
                                      widget.content.orderGroupId?.toInt() ?? 0,
                                      widget.categoryId ?? "",
                                      date: widget.date,
                                      steelMr: widget.steelMr,
                                    );
                              }
                            });
                          },
                          child: Column(
                            children: [
                              buildRow(
                                'Discount',
                                discount.toStringAsFixed(2),
                                isBuyer()
                                    ? style2
                                    : style2.copyWith(
                                        decoration: TextDecoration.underline,
                                      ),
                                showPencil: true && !isBuyer(),
                              ),
                              buildRow(
                                'Total After Discount',
                                totalAfterDiscount.toStringAsFixed(2),
                                isBuyer()
                                    ? style2
                                    : style2.copyWith(
                                        decoration: TextDecoration.underline,
                                      ),
                                showPencil: true && !isBuyer(),
                              ),
                            ],
                          ),
                        ),
                        buildRow('Total GST', gst.toStringAsFixed(2), style2),
                        GestureDetector(
                          onTap: () {
                            if (isBuyer()) {
                              return;
                            }
                            showDialog(
                                context: context,
                                builder: (context) {
                                  return DiscountTransportationDialog(
                                    orderGroupId:
                                        widget.content.orderGroupId?.toInt() ??
                                            0,
                                    sellerId: getVendorId(),
                                    categoryId: widget.content.cappCategoriesId
                                            ?.toInt() ??
                                        0,
                                    offerIds: offerIdList,
                                  );
                                }).then((value) {
                              if (value == true) {
                                var req = ViewOfferReq(
                                  variant1OptionGroupName: [],
                                  variant1OptionNames: [],
                                  variant2OptionGroupName: [],
                                  variant2OptionNames: [],
                                  variant3OptionGroupName: [],
                                  variant3OptionNames: [],
                                );
                                context.read<OffersCubit>().loadSellerOffers(
                                      widget.content.prchOrdrId.toString(),
                                      req,
                                      widget.content.orderGroupId?.toInt() ?? 0,
                                      widget.categoryId ?? "",
                                      date: widget.date,
                                      steelMr: widget.steelMr,
                                    );
                              }
                            });
                          },
                          child: buildRow(
                            'Transportation',
                            transportation.toStringAsFixed(2),
                            isBuyer()
                                ? style2
                                : style2.copyWith(
                                    decoration: TextDecoration.underline,
                                  ),
                            showPencil: true && !isBuyer(),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            if (isBuyer()) {
                              return;
                            }
                            showDialog(
                                context: context,
                                builder: (context) {
                                  return DiscountTransportationDialog(
                                    orderGroupId:
                                        widget.content.orderGroupId?.toInt() ??
                                            0,
                                    sellerId: getVendorId(),
                                    categoryId: widget.content.cappCategoriesId
                                            ?.toInt() ??
                                        0,
                                    offerIds: offerIdList,
                                  );
                                }).then((value) {
                              if (value == true) {
                                var req = ViewOfferReq(
                                  variant1OptionGroupName: [],
                                  variant1OptionNames: [],
                                  variant2OptionGroupName: [],
                                  variant2OptionNames: [],
                                  variant3OptionGroupName: [],
                                  variant3OptionNames: [],
                                );
                                context.read<OffersCubit>().loadSellerOffers(
                                      widget.content.prchOrdrId.toString(),
                                      req,
                                      widget.content.orderGroupId?.toInt() ?? 0,
                                      widget.categoryId ?? "",
                                      date: widget.date,
                                      steelMr: widget.steelMr,
                                    );
                              }
                            });
                          },
                          child: buildRow(
                            'Loading',
                            loading.toStringAsFixed(2),
                            isBuyer()
                                ? style2
                                : style2.copyWith(
                                    decoration: TextDecoration.underline,
                                  ),
                            showPencil: true && !isBuyer(),
                          ),
                        ),
                        buildRow('GST on Trans. & Loading',
                            gstOnTransportation.toStringAsFixed(2), style2),
                        GestureDetector(
                          onTap: () {
                            // alert(getVendorId());
                            if (isBuyer()) {
                              return;
                            }
                            showDialog(
                                context: context,
                                builder: (context) {
                                  return RoundoffDiscountDialog(
                                    orderGroupId:
                                        widget.content.orderGroupId?.toInt() ??
                                            0,
                                    currentTotalAmount: total.toDouble(),
                                    roundoffDiscount:
                                        roundoffDiscount.toDouble(),
                                    offerId: offerIdList,
                                  );
                                }).then((value) {
                              if (value == true) {
                                var req = ViewOfferReq(
                                  variant1OptionGroupName: [],
                                  variant1OptionNames: [],
                                  variant2OptionGroupName: [],
                                  variant2OptionNames: [],
                                  variant3OptionGroupName: [],
                                  variant3OptionNames: [],
                                );
                                context.read<OffersCubit>().loadSellerOffers(
                                      widget.content.prchOrdrId.toString(),
                                      req,
                                      widget.content.orderGroupId?.toInt() ?? 0,
                                      widget.categoryId ?? "",
                                      date: widget.date,
                                      steelMr: widget.steelMr,
                                    );
                              }
                            });
                          },
                          child: Column(
                            children: [
                              buildRow(
                                'Roundoff Discount',
                                discount.toStringAsFixed(2),
                                isBuyer()
                                    ? style2
                                    : style2.copyWith(
                                        decoration: TextDecoration.underline,
                                      ),
                                showPencil: true && !isBuyer(),
                              ),
                            ],
                          ),
                        ),
                        buildRow(
                            'Total Amount', total.toStringAsFixed(2), style2),
                        if (!isLowest)
                          Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    resetProductItem();
                                  },
                                  child: Container(
                                    decoration: BoxDecoration(
                                        border: Border.all(width: 0.75),
                                        color: AppColors.red),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8),
                                      child: Text(
                                        'Return To The Lowest Rates',
                                        style: style2.copyWith(
                                            color: Colors.white),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  );
                },
              );
            },
          )
        ],
      ),
    );
  }

  Widget buildRow(
    String label,
    String value,
    TextStyle style, {
    bool showPencil = false,
  }) {
    return Container(
      decoration: const BoxDecoration(),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              flex: 61,
              child: Container(
                decoration: BoxDecoration(border: Border.all(width: 0.75)),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(8, 0, 8, 0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(label, style: style),
                      const SizedBox(width: 4),
                      if (showPencil)
                        const CircleAvatar(
                          backgroundColor: Colors.amber,
                          radius: 10,
                          child: Icon(
                            Icons.edit,
                            size: 10,
                            color: Colors.black,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
            Expanded(
              flex: 50,
              child: Container(
                decoration: BoxDecoration(border: Border.all(width: 0.75)),
                child: Padding(
                  padding: const EdgeInsets.all(8.0), // Add more padding
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      value, // Removed toRupeeFormat() for simplicity - add it back if needed
                      style: style,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SummaryQuote extends StatelessWidget {
  const SummaryQuote({
    required this.isLowest,
    this.show = true,
    Key? key,
  }) : super(key: key);

  final bool isLowest;
  final bool show;

  @override
  Widget build(BuildContext context) {
    TextStyle style2 =
        const TextStyle(fontWeight: FontWeight.bold, fontSize: 12);
    TextStyle style1 = const TextStyle(
        fontWeight: FontWeight.bold, color: Colors.white, fontSize: 12);

    return Center(
      child: Table(
        border: TableBorder.all(
          color: Colors.black,
          width: 0.75,
        ),
        children: [
          TableRow(
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Center(
                  child: Text('Summary of Quote',
                      style: style2.copyWith(fontSize: 14)),
                ),
              ),
            ],
          ),
          if (show)
            TableRow(
              children: [
                Container(
                  color: Colors.orange.shade800,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Center(
                      child: Text(
                        isLowest
                            ? 'Summary Showing Lowest Rate Combination'
                            : 'Summary Showing Rate Of Selected Combination',
                        style: style1,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }
}

class SiteNameBox extends StatelessWidget {
  final String name;

  const SiteNameBox({Key? key, required this.name}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    TextStyle style =
        const TextStyle(fontWeight: FontWeight.bold, fontSize: 12);
    return Center(
      child: Table(
        columnWidths: const {
          1: FlexColumnWidth(
              3), // First column takes twice the space of the second column
        },
        border: TableBorder.all(
          color: Colors.black, // Border color
          width: 0.75, // Border width
        ),
        children: [
          TableRow(
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text('Site:', style: style),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(name, style: style.copyWith(fontSize: 14)),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class SplitNameBox extends StatelessWidget {
  final String name;
  final String? splitName;

  const SplitNameBox({
    Key? key,
    required this.name,
    this.splitName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    TextStyle style =
        const TextStyle(fontWeight: FontWeight.bold, fontSize: 12);
    return Center(
      child: Table(
        columnWidths: const {
          1: FlexColumnWidth(
              3), // First column takes twice the space of the second column
        },
        border: TableBorder.all(
          color: Colors.black, // Border color
          width: 0.75, // Border width
        ),
        children: [
          TableRow(
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text('Material Group:', style: style),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(splitName != null ? splitName! : name,
                    style: style.copyWith(fontSize: 14)),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class SiteAccess extends StatelessWidget {
  final String name;

  const SiteAccess({Key? key, required this.name}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    TextStyle style =
        const TextStyle(fontWeight: FontWeight.bold, fontSize: 12);
    return Center(
      child: Table(
        columnWidths: const {
          1: FlexColumnWidth(3),
        },
        border: TableBorder.all(
          color: Colors.black, // Border color
          width: 0.75, // Border width
        ),
        children: [
          TableRow(
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text('Site Access:', style: style),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(8, 16, 0, 16),
                child: Text(name, style: style),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class VendorNameBox extends StatelessWidget {
  final String vendorname;
  final int? vendorPhone;

  const VendorNameBox({
    Key? key,
    required this.vendorname,
    this.vendorPhone,
  }) : super(key: key);
  @override
  Widget build(BuildContext context) {
    TextStyle style = const TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 12,
    );
    return Center(
      child: Table(
        border: TableBorder.all(
          color: Colors.black,
          width: 0.75,
        ),
        columnWidths: const {
          0: FlexColumnWidth(3),
          1: FlexColumnWidth(9),
        },
        children: [
          TableRow(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10),
                child: Text(
                  isBuyer() ? 'Vendor Name' : 'Buyer Name',
                  style: style,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              GestureDetector(
                onTap: () {
                  if (vendorPhone != null) {
                    _makePhoneCall('tel:$vendorPhone');
                  } else {
                    alert('Phone number is not available.');
                  }
                },
                child: Container(
                  color: const Color(0xFF1A60A2).withOpacity(0.33),
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          vendorname,
                          style: style.copyWith(
                            decoration: TextDecoration.underline,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Icon(
                        Icons.call,
                        color: vendorPhone == null
                            ? Colors.transparent
                            : AppColors.primaryColorOld,
                        size: 20,
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  void _makePhoneCall(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      alert("Could not call the number.");
    }
  }
}

class ProductItem extends StatefulWidget {
  final List<MatchedProduct>? match;
  final Function(double, double) onSumChange;
  final Function(List<Detail?>) onDetailChange;
  final Function(int)? prchOrdrOffrId;
  final bool? fromAssign;
  final String statusCd;
  final num? dcnt;
  final num? tnChrg;

  const ProductItem({
    Key? key,
    required this.match,
    required this.onSumChange,
    required this.onDetailChange,
    this.prchOrdrOffrId,
    this.fromAssign,
    required this.statusCd,
    required this.dcnt,
    required this.tnChrg,
  }) : super(key: key);

  @override
  State<ProductItem> createState() => _ProductItemState();
}

class _ProductItemState extends State<ProductItem> {
  List<String?> selectedItems = [];
  List<Detail?> selectedDetails = [];

  @override
  void initState() {
    super.initState();
    if (widget.match != null && widget.match!.isNotEmpty) {
      for (var product in widget.match!) {
        if (product.details != null && product.details!.isNotEmpty) {
          selectedDetails.add(product.details![0]);
          selectedItems.add(product.details![0].brand);

          // Triggering the callback initially if prchOrdrOffrId is available
          if (widget.prchOrdrOffrId != null &&
              product.details![0].prchOrdrOffrId != null) {
            widget.prchOrdrOffrId!(
                product.details![0].prchOrdrOffrId?.toInt() ?? 0);
            OrderStorage.addOrUpdateOrderPair(
              product.details![0].prchOrdrId.toString(),
              product.details![0].sellerId.toString(),
              product.details![0].prchOrdrOffrId.toString(),
            );
          }
        } else {
          selectedDetails.add(null);
          selectedItems.add(null);
        }
      }
    }
    // Calculate the initial sum and trigger the callback
    _updateOfferPriceSum();
  }

  void reset() {
    setState(() {
      selectedDetails.clear();
      selectedItems.clear();
      if (widget.match != null && widget.match!.isNotEmpty) {
        for (var product in widget.match!) {
          if (product.details != null && product.details!.isNotEmpty) {
            selectedDetails.add(product.details![0]);
            selectedItems.add(product.details![0].brand);

            // Triggering the callback initially if prchOrdrOffrId is available
            if (widget.prchOrdrOffrId != null &&
                product.details![0].prchOrdrOffrId != null) {
              widget.prchOrdrOffrId!(
                  product.details![0].prchOrdrOffrId?.toInt() ?? 0);
              OrderStorage.addOrUpdateOrderPair(
                product.details![0].prchOrdrId.toString(),
                product.details![0].sellerId.toString(),
                product.details![0].prchOrdrOffrId.toString(),
              );
            }
          } else {
            selectedDetails.add(null);
            selectedItems.add(null);
          }
        }
      }
      _updateOfferPriceSum();
    });
  }

  void _updateOfferPriceSum() {
    double offerPriceSum = 0.0;
    double gstSum = 0.0;
    List<int> offerIds = [];

    for (var detail in selectedDetails) {
      if (detail != null) {
        // call cubit calculation method here
        if (detail.subProducts?.isNotEmpty ?? false) {
          // Steel product
          double steelTotalPrice = 0.0;
          double steelGst = 0.0;
          for (var subProduct in detail.subProducts!) {
            var offerId = subProduct.subProductOfferId?.toInt();
            offerIds.add(offerId ?? 0);
            steelTotalPrice += subProduct.subProductTotalPrice ?? 0.0;
            steelGst += (((subProduct.subProductTotalPrice ?? 0.0) *
                    (detail.gst ?? 0)) /
                100);
          }
          offerPriceSum += steelTotalPrice;
          gstSum += steelGst;
        } else {
          // Non-steel product
          var offerId = detail.prchOrdrOffrId;
          offerIds.add(offerId ?? 0);
          offerPriceSum += detail.offerPrice ?? 0.0;
          gstSum += (((detail.offerPrice ?? 0.0) * (detail.gst ?? 0)) / 100);
        }
      }
    }
    // Call the callback to update the parent widget with the new sum
    widget.onSumChange(offerPriceSum, gstSum);
    widget.onDetailChange(selectedDetails);
    context.read<CalculatorCubit>().calculate(
          offerIds,
          widget.tnChrg ?? 0,
          widget.dcnt ?? 0,
        );
  }

  void _openQuotesDialog(int prchOrdrId, bool submitEnabled,
      {String? brand = "", Detail? detail}) {
    if (isBuyer()) {
      showDialog(
        context: context,
        builder: (context) => BuyerQuotesDialog(
          prchOrdrId: prchOrdrId,
          brand: brand,
        ),
      );
    } else if (submitEnabled) {
      showDialog(
        context: context,
        builder: (context) => SellerQuotesDialog(
          prchOrdrId: prchOrdrId,
          itemDetails: detail,
        ),
      );
    } else {
      properAlertWithOk(
        "This MR is currently not accepting offers. Do you want to proceed?",
        () {
          Navigator.pop(context);
          showDialog(
            context: context,
            builder: (context) => SellerQuotesDialog(
              prchOrdrId: prchOrdrId,
              itemDetails: detail,
            ),
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    TextStyle style2 = const TextStyle(
      overflow: TextOverflow.ellipsis,
      color: Colors.black,
      fontWeight: FontWeight.bold,
      fontSize: 12,
    );

    var mrStatus = widget.statusCd;
    var enableSubmit = ["QUOT", "NEGO", "VASS"].contains(mrStatus);
    var enableDropdown = [
      "NEWW",
      "REJD",
      "EDIT",
      "SPLT",
      "QUOT",
      "NEGO",
      "VASS"
    ].contains(mrStatus);

    return Table(
      children: List.generate(widget.match?.length ?? 0, (index) {
        // var isSteel = widget.match?[index].productName?.contains("steel reinforcement")?? false;
        var isSteel = selectedDetails[index]?.subProducts?.isNotEmpty ?? false;
        var product = widget.match?[index];
        var details = product?.details ?? [];

        List<String> dropdownItems =
            details.map((detail) => detail.brand ?? '').toSet().toList();

        // Set the initial selectedDetail for the current index if not already set
        selectedDetails[index] ??= details.isNotEmpty ? details[0] : null;

        // if DSCB then discarded by buyer
        // if DSCS discarded by seller
        // if null then not discarded
        var discardedByBuyer = selectedDetails[index]?.discardStatus == 'DSCB';
        var discardedBySeller = selectedDetails[index]?.discardStatus == 'DSCS';
        var isDiscarded = (discardedByBuyer || discardedBySeller) && !isSteel;
        var discardEnabled = false;
        var discardAllowed =
            ['QUOT', 'NEGO', 'VASS'].contains(widget.statusCd) && !isSteel;
        if (isBuyer() && discardAllowed && !isSteel) {
          if (selectedDetails[index]?.discardStatus == 'DSCB' ||
              selectedDetails[index]?.discardStatus == null) {
            discardEnabled = true;
          }
        } else if (!isBuyer() && discardAllowed && !isSteel) {
          if (selectedDetails[index]?.discardStatus == 'DSCS' ||
              selectedDetails[index]?.discardStatus == null) {
            discardEnabled = true;
          }
        }

        // var statusCd = selectedDetails[index]?.statusCd;

        var isQuantityEdited = selectedDetails[index]?.quantityChangeYn == 'Y';
        var isNego = selectedDetails[index]?.buyerNegoYN == 'Y' ||
            selectedDetails[index]?.sellerNegoYN == 'Y';

        Color color;
        if (isNego) {
          color = Colors.yellow;
        }

        color = selectedDetails[index]?.isAccepted == true
            ? Colors.green.shade200
            : Colors.white;

        if (isDiscarded) {
          color = Colors.redAccent.shade100;
        }

        double productAmount;
        if (isSteel) {
          productAmount = selectedDetails[index]!.subProducts!.fold(
              0.0,
              (sum, subProduct) =>
                  sum + (subProduct.subProductTotalPrice ?? 0.0));
        } else {
          productAmount = selectedDetails[index]?.offerPrice?.toDouble() ?? 0.0;
        }

        return TableRow(
          children: [
            // First column
            Column(
              children: [
                // Same for all
                Row(children: [
                  Expanded(
                    flex: 12,
                    child: Container(
                      decoration: BoxDecoration(
                        border: const Border(
                          bottom: BorderSide.none,
                          left: BorderSide(
                            width: 0.75,
                            color: Colors.black,
                          ),
                          right: BorderSide(
                            width: 0.75,
                            color: Colors.black,
                          ),
                          top: BorderSide(
                            width: 0.75,
                            color: Colors.black,
                          ),
                        ),
                        color: color,
                      ),
                      height: 40,
                      child: Center(
                        child: Text(
                          '${index + 1}',
                          style: style2.copyWith(fontSize: 14),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 49,
                    child: GestureDetector(
                      onTap: () {
                        _openMr(index, product);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(width: 0.75),
                          color: color,
                        ),
                        padding: const EdgeInsets.only(left: 2),
                        height: 40,
                        child: Align(
                          alignment: Alignment.center,
                          child: Text(
                            product?.productName ?? '',
                            style: style2.copyWith(
                              decoration: TextDecoration.underline,
                            ),
                            textAlign: TextAlign.start,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 50,
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(width: 0.75),
                        // color: const Color(0xFF1A60A2).withOpacity(0.33),
                        color: color,
                      ),
                      height: 40,
                      child: Center(
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            padding: EdgeInsets.zero,
                            icon: Row(
                              children: [
                                const Icon(Icons.arrow_drop_down_rounded),
                                if (enableSubmit) // Only show edit icon if enableSubmit is true
                                  CircleAvatar(
                                    backgroundColor: Colors.amber,
                                    radius: 10,
                                    child: IconButton(
                                      onPressed: () {
                                        _openQuotesDialog(
                                          selectedDetails[index]
                                                  ?.prchOrdrId
                                                  ?.toInt() ??
                                              0,
                                          enableSubmit,
                                          brand:
                                              selectedDetails[index]?.brand2 ??
                                                  "",
                                          detail: selectedDetails[index],
                                        );
                                      },
                                      icon: const Icon(
                                        Icons.edit,
                                        size: 14,
                                        color: Colors.black,
                                      ),
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                    ),
                                  ),
                              ],
                            ),
                            value: selectedItems[index],
                            hint: Text(
                              'Select an option',
                              style: style2,
                            ),
                            onChanged: enableDropdown
                                ? (String? newValue) {
                                    setState(() {
                                      selectedItems[index] = newValue;
                                      selectedDetails[index] =
                                          details.firstWhere((detail) =>
                                              detail.brand == newValue);
                                      _updateOfferPriceSum();
                                      if (widget.prchOrdrOffrId != null &&
                                          selectedDetails[index]
                                                  ?.prchOrdrOffrId !=
                                              null) {
                                        widget.prchOrdrOffrId!(
                                            selectedDetails[index]!
                                                    .prchOrdrOffrId
                                                    ?.toInt() ??
                                                0);
                                        OrderStorage.addOrUpdateOrderPair(
                                          selectedDetails[index]!
                                              .prchOrdrId
                                              .toString(),
                                          selectedDetails[index]!
                                              .sellerId
                                              .toString(),
                                          selectedDetails[index]!
                                              .prchOrdrOffrId
                                              .toString(),
                                        );
                                      }
                                    });
                                  }
                                : null, // Disable dropdown if enableSubmit is false
                            items: dropdownItems.map((String item) {
                              return DropdownMenuItem<String>(
                                value: item,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      item.removeFromLastParenthesis(),
                                      maxLines: 2,
                                      style: style2,
                                    ),
                                    if (item == selectedItems[index] &&
                                        dropdownItems.length > 1) ...[
                                      const SizedBox(width: 4),
                                      Text(
                                        "(+${dropdownItems.length - 1})",
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                  ),
                ]),
                // For cement
                if (!isSteel)
                  Row(
                    children: [
                      Expanded(
                        flex: 12,
                        child: GestureDetector(
                          onTap: () {
                            if (discardEnabled) {
                              context.read<OffersCubit>().discardOffer(
                                    prchOrdrOffrId: selectedDetails[index]
                                            ?.prchOrdrOffrId
                                            .toString() ??
                                        "",
                                    isDiscard: !isDiscarded,
                                  );
                            }
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              border: const Border(
                                top: BorderSide.none,
                                left: BorderSide(
                                  width: 0.75,
                                  color: Colors.black,
                                ),
                                right: BorderSide(
                                  width: 0.75,
                                  color: Colors.black,
                                ),
                                bottom: BorderSide(
                                  width: 0.75,
                                  color: Colors.black,
                                ),
                              ),
                              color: color,
                            ),
                            height: 40,
                            // only if statusCd is QUOT, NEGO or VASS show icon otherwise null
                            child: Center(
                                child: (discardEnabled)
                                    ? isDiscarded
                                        ? Icon(
                                            Icons.undo,
                                            size: 18,
                                            color: (widget.fromAssign ?? false)
                                                ? Colors.transparent
                                                : Colors.black,
                                          )
                                        : Icon(
                                            Icons.delete,
                                            size: 18,
                                            color: (widget.fromAssign ?? false)
                                                ? Colors.transparent
                                                : Colors.black,
                                          )
                                    : const SizedBox()),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 49,
                        child: GestureDetector(
                          onTap: () {
                            _openMr(index, product);
                          },
                          child: Row(
                            children: [
                              Expanded(
                                child: Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(width: 0.75),
                                    color: color,
                                  ),
                                  height: 40,
                                  child: Center(
                                    child: Text(
                                      selectedDetails[index]!
                                              .quantity
                                              .toString()
                                              .toDisplayString() +
                                          (isQuantityEdited ? " *" : ""),
                                      style: style2.copyWith(
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(width: 0.75),
                                    color: color,
                                  ),
                                  height: 40,
                                  child: Center(
                                    child: Text(
                                      selectedDetails[index]
                                              ?.unit
                                              .toString()
                                              .toDisplayString() ??
                                          'Unit',
                                      style: style2.copyWith(
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 50,
                        child: GestureDetector(
                          onTap: () {
                            _openQuotesDialog(
                              selectedDetails[index]?.prchOrdrId?.toInt() ?? 0,
                              enableSubmit,
                              brand: selectedDetails[index]?.brand2 ?? "",
                              detail: selectedDetails[index],
                            );
                          },
                          child: Row(
                            children: [
                              Expanded(
                                child: Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(width: 0.75),
                                    color: color,
                                  ),
                                  height: 40,
                                  child: Center(
                                    child: Text(
                                      selectedDetails[index]
                                              ?.perUnitPrice
                                              ?.toStringAsFixed(2) ??
                                          '',
                                      style: style2.copyWith(
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(width: 0.75),
                                    color: color,
                                  ),
                                  height: 40,
                                  child: Center(
                                    child: Text(
                                      productAmount.toStringAsFixed(2),
                                      style: style2.copyWith(
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ////////////////////////////////////////////////////////////////
                if (isSteel)
                  Column(
                    children: selectedDetails[index]!.subProducts!.map((val) {
                      return Row(children: [
                        Expanded(
                          flex: 12,
                          child: Column(
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  border: const Border(
                                    bottom: BorderSide.none,
                                    left: BorderSide(
                                      width: 0.75,
                                      color: Colors.black,
                                    ),
                                    right: BorderSide(
                                      width: 0.75,
                                      color: Colors.black,
                                    ),
                                    top: BorderSide(
                                      width: 0.75,
                                      color: Colors.black,
                                    ),
                                  ),
                                  color:
                                      selectedDetails[index]?.isAccepted == true
                                          ? Colors.green.shade100
                                          : Colors.white,
                                ),
                                height: 60,
                                child: Center(
                                  child: Text(
                                    'S${selectedDetails[index]!.subProducts!.indexOf(val) + 1}',
                                    style: style2,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 49,
                          child: GestureDetector(
                            onTap: () {
                              _openQuotesDialog(
                                selectedDetails[index]?.prchOrdrId?.toInt() ??
                                    0,
                                enableSubmit,
                                brand: selectedDetails[index]?.brand2 ?? "",
                                detail: selectedDetails[index],
                              );
                            },
                            child: Column(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(width: 0.75),
                                    color: selectedDetails[index]?.isAccepted ==
                                            true
                                        ? Colors.green.shade100
                                        : Colors.white,
                                  ),
                                  height: 30,
                                  child: Center(
                                    child: Text(
                                      val.subProductName?.toString() ?? "N/A",
                                      style: style2,
                                      maxLines: 2,
                                    ),
                                  ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(width: 0.75),
                                    color: selectedDetails[index]?.isAccepted ==
                                            true
                                        ? Colors.green.shade100
                                        : Colors.white,
                                  ),
                                  height: 30,
                                  child: Center(
                                    child: Text(
                                      val.subProductType?.toString() ?? "N/A",
                                      style: style2,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 50,
                          child: GestureDetector(
                            onTap: () {
                              _openQuotesDialog(
                                selectedDetails[index]?.prchOrdrId?.toInt() ??
                                    0,
                                enableSubmit,
                                brand: selectedDetails[index]?.brand2 ?? "",
                                detail: selectedDetails[index],
                              );
                            },
                            child: Row(
                              children: [
                                Expanded(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      border: Border.all(width: 0.75),
                                      color:
                                          selectedDetails[index]?.isAccepted ==
                                                  true
                                              ? Colors.green.shade100
                                              : Colors.white,
                                    ),
                                    height: 60,
                                    child: Center(
                                      child: Text(
                                        val.subProductPricePerUnit
                                                ?.toStringAsFixed(2) ??
                                            '0.0',
                                        style: style2.copyWith(
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      border: Border.all(width: 0.75),
                                      color:
                                          selectedDetails[index]?.isAccepted ==
                                                  true
                                              ? Colors.green.shade100
                                              : Colors.white,
                                    ),
                                    height: 60,
                                    child: Center(
                                      child: Text(
                                        val.subProductTotalPrice
                                                ?.toStringAsFixed(2) ??
                                            '0.0',
                                        style: style2.copyWith(
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ]);
                    }).toList(),
                  ),
              ],
            )
          ],
        );
      }),
    );
  }

  void _openMr(int index, MatchedProduct? product) {
    var item = selectedDetails[index];
    Get.to(
      SingleMRScreen(
        notificationData: NotificationData(
          code: product?.productName?.toString() ?? "N/A",
          subject: product?.productName?.toString() ?? "N/A",
          event: '',
          content: item?.categoryName.toString() ?? "N/A",
          id: item?.prchOrdrId,
        ),
      ),
    );
  }
}

class MrAndDateBox extends StatelessWidget {
  final String mrnum;
  final String date;

  const MrAndDateBox({Key? key, required this.mrnum, required this.date})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    TextStyle style =
        const TextStyle(fontWeight: FontWeight.bold, fontSize: 12);
    return Center(
      child: Table(
        columnWidths: const {
          0: FlexColumnWidth(
              1), // First column takes twice the space of the second column
        },
        border: TableBorder.all(
          color: Colors.black, // Border color
          width: 0.75, // Border width
        ),
        children: [
          TableRow(
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text('MR No:', style: style),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(mrnum, style: style),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text('Date', style: style),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  date,
                  style: style,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class QuotationNoAndDateBox extends StatelessWidget {
  final String quotationDate;
  final String quotation;

  const QuotationNoAndDateBox(
      {Key? key, required this.quotationDate, required this.quotation})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    TextStyle style =
        const TextStyle(fontWeight: FontWeight.bold, fontSize: 12);
    return Center(
      child: Table(
        columnWidths: const {
          0: FlexColumnWidth(
              1), // First column takes twice the space of the second column
        },
        border: TableBorder.all(
          color: Colors.black, // Border color
          width: 0.75, // Border width
        ),
        children: [
          TableRow(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8),
                child: Text(
                  'Qtn. No:',
                  style: style,
                  maxLines: 2,
                ),
              ),
              Container(
                color: const Color(0xFF1A60A2).withOpacity(0.33),
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  quotation,
                  style: style,
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  'Qtn. Date',
                  style: style,
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8.0, horizontal: 0),
                child: Text(
                  quotationDate,
                  style: style,
                  maxLines: 1,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.clip,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class BuyerNameBox extends StatelessWidget {
  final String name;

  const BuyerNameBox({Key? key, required this.name}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    TextStyle style =
        const TextStyle(fontWeight: FontWeight.bold, fontSize: 14);
    return Center(
      child: Table(
        border: TableBorder.all(
          color: Colors.black, // Border color
          width: 0.75, // Border width
        ),
        children: [
          TableRow(
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  name,
                  style: style,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
